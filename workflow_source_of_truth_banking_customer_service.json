{"id": "banking_customer_service", "name": "Banking Customer Service", "version": "2.1", "states": {"Greeting": {"type": "input", "layer2_id": "l2_greeting_banking_system_v2", "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "Inquiry": {"type": "inform", "layer2_id": "l2_inquiry_banking_system_v2", "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "RepeatedUserQuery": {"type": "inform", "layer2_id": "l2_repeated_user_query_simple", "allowed_tools": ["STT", "LLM", "TTS"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "CheckBalance": {"type": "inform", "layer2_id": "l2_check_balance_banking_system_v2", "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS", "account_id"]}, "TransferFunds": {"type": "transaction", "layer2_id": "l2_transfer_funds_banking_system_v2", "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "expected_input": ["account_id", "amount", "currency"], "expected_output": ["audio_path", "latencyTTS"]}, "ReverseTransferFunds": {"type": "transaction", "layer2_id": "l2_reverse_transfer_funds", "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "ConfirmReverse": {"type": "confirm", "layer2_id": "l2_confirm_reverse", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "CancelReverse": {"type": "cancel", "layer2_id": "l2_cancel_reverse", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "TransferFundsInputFallback": {"type": "input", "layer2_id": "l2_transfer_funds_input_fallback_banking_system_v2", "allowed_tools": ["STT", "LLM", "TTS"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS", "account_id", "amount", "currency"]}, "exchangeRate": {"type": "inform", "layer2_id": "l2_exchange_rate_banking_system_v2", "allowed_tools": ["LLM", "TTS", "EXCHANGE_API"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "Goodbye": {"type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}}, "rules": [{"from": "Greeting", "condition": "true", "to": "Inquiry"}, {"from": "Inquiry", "condition": "intent == 'account_balance'", "to": "BalancePhase"}, {"from": "Inquiry", "condition": "intent == 'fund_transfer'", "to": "TransferPhase"}, {"from": "Inquiry", "condition": "intent == 'exchange_rate'", "to": "ExchangePhase"}, {"from": "Inquiry", "condition": "intent == 'goodbye'", "to": "Goodbye"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'account_balance'", "to": "BalancePhase"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'fund_transfer'", "to": "TransferPhase"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'exchange_rate'", "to": "ExchangePhase"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'undo'", "to": "last_anti_state"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'goodbye'", "to": "Goodbye"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "CheckBalance", "condition": "true", "to": "TransferPhase"}, {"from": "TransferFunds", "condition": "amount >= 60000", "to": "Goodbye"}, {"from": "TransferFunds", "condition": "balance < amount", "to": "BalancePhase"}, {"from": "TransferFunds", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "ReverseTransferFunds", "condition": "intent == 'confirm'", "to": "ConfirmReverse"}, {"from": "ReverseTransferFunds", "condition": "intent == 'cancel'", "to": "CancelReverse"}, {"from": "ReverseTransferFunds", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "ConfirmReverse", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "CancelReverse", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "TransferFundsInputFallback", "condition": "true", "to": "TransferFunds"}, {"from": "exchangeRate", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_actions": ["Check account balance", "Transfer funds", "Get exchange rates", "Multiple requests in same session"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification"], "allowed_tools": ["TRANSACTION_API", "STT", "CACHE", "TTS", "EXCHANGE_API", "LLM", "DB_QUERY"]}